// src/lib/auth.ts
import { betterAuth } from "better-auth";
import { prisma } from '@/db';
import { prismaAdapter } from "better-auth/adapters/prisma";
import { nextCookies } from "better-auth/next-js";

console.log('Auth file loaded');

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: 'mongodb',
  }),
  plugins: [
    nextCookies(),
  ],
  databaseHooks: {
    user: {
      create: {
        before: async (userData) => {
          console.log('userData', userData);
          // -- 1) Ensure we have an email
          const email = userData.email?.toLowerCase() || "";
          // -- 2) Check domain
          if (!email.endsWith("@onecarnow.com")) {
            // Abort creation with a 400-style error
            // throw new APIError("BAD_REQUEST", {
            //   message: "Email not allowed",
            // });
            throw new Error("Email not allowed");
          }
          // -- 3) Return the (possibly modified) payload
          return { data: userData };
        },
        // you can also define an `after` hook here if needed
      },
    },
  },
  socialProviders: {
    google: {
      enabled: true,
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    },
  },
});
