// server.ts
import { treaty } from '@elysiajs/eden'
import { App } from './app'

const server = treaty<App>(process.env.NEXT_PUBLIC_APP_URL || 'localhost:4002')

export default server;

// Usage example:
// const { data, error } = await server.api.rooms.get()

// if (error) {
//   console.error(error)
// } else {
//   console.log(data)
// }

// more examples:

server.api.bookings({ id: '' })
server.api.bookings({ id: '' })['check-in'].post({
  // body here:
})

server.api.bookings.stats.get({
  query: {
    startDate: '',
    endDate: ''
  }
})