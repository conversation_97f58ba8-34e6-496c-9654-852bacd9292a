# OCN House - Sistema de Gestión de Oficinas

## 📋 Resumen del Proyecto

Sistema integral para la gestión de espacios de oficina de OneCarNow, incluyendo reservas de salas de reunión, escritorios, gestión de huéspedes e invitaciones con confirmaciones por Slack.

## 🏗️ Arquitectura Técnica

### Stack Tecnológico
- **Frontend**: Next.js 15.3.4 con TypeScript
- **Backend**: Elysia.js con modular API structure
- **Base de Datos**: MongoDB con Prisma ORM
- **Autenticación**: Better Auth (configurado, pendiente integración)
- **Estado**: React Query + Zustand
- **UI**: Shadcn/ui con Tailwind CSS
- **Fechas**: <PERSON><PERSON> (evitando moment.js)
- **Calendario**: React Big Calendar con luxonLocalizer
- **Gestión de Paquetes**: Bun

### Estructura del Proyecto
```
src/
├── app/
│   ├── api/
│   │   ├── modules/          # API modular con Elysia
│   │   │   ├── bookings/
│   │   │   ├── desks/
│   │   │   ├── guests/
│   │   │   └── meeting-rooms/
│   │   └── server.ts         # Treaty client configuration
│   ├── dashboard/            # Rutas del dashboard
│   │   ├── admin/           # Panel de administración
│   │   ├── bookings/        # Gestión de reservas
│   │   ├── desks/           # Escritorios
│   │   ├── guests/          # Gestión de huéspedes
│   │   ├── meeting-rooms/   # Salas de reunión
│   │   └── reception/       # Dashboard de recepción
│   └── page.tsx             # Login con blur background
├── lib/
│   ├── hooks/               # React Query hooks
│   ├── services/            # Servicios (email, Slack, WhatsApp)
│   ├── store/               # Zustand stores
│   └── validators/          # Zod schemas
└── components/ui/           # Shadcn components
```

## 🎯 Funcionalidades Implementadas

### ✅ Autenticación y Usuarios
- Login page con diseño blur y background.avif
- Better Auth configurado (pendiente integración con Google OAuth)
- Control de acceso por dominio @onecarnow.com

### ✅ Gestión de Salas de Reunión
- **CRUD completo**: Crear, leer, actualizar, eliminar salas
- **Reservas**: Sistema de reservas con intervalos de 15 minutos
- **Reglas de negocio**: Duración mín/máx, días de anticipación, aprobación
- **Disponibilidad**: Horarios de negocio y días laborales
- **Amenidades**: WiFi, proyector, TV, pizarra, etc.
- **Ubicación**: Edificio, piso, sala, dirección

### ✅ Gestión de Escritorios
- **Tipos**: Regular, Standing, Ejecutivo
- **Reservas**: Por día completo
- **Ubicación**: Piso, zona, posición
- **Equipamiento**: Monitor, laptop, accesorios

### ✅ Sistema de Reservas
- **Calendario integrado**: React Big Calendar con Luxon
- **Tipos de recursos**: Salas de reunión y escritorios
- **Estados**: PENDING, CONFIRMED, CANCELLED, COMPLETED
- **Validaciones**: Conflictos, disponibilidad, reglas de negocio
- **Huéspedes**: Invitación opcional de huéspedes externos

### ✅ Gestión de Huéspedes
- **Invitaciones**: Sistema completo de invitación de huéspedes
- **Estados**: PENDING, CONFIRMED, CHECKED_IN, CHECKED_OUT, CANCELLED, NO_SHOW
- **Check-in/out**: Dashboard de recepción para gestión
- **Notificaciones**: Email y Slack automáticos
- **Asociación**: Vinculación opcional con reservas

### ✅ Dashboard de Recepción
- **Vista diaria**: Huéspedes esperados para hoy
- **Check-in/out**: Gestión en tiempo real
- **Estadísticas**: Contadores y métricas del día
- **Búsqueda**: Filtrado por nombre, email, empresa

### ✅ Panel de Administración
- **Gestión de recursos**: CRUD para salas y escritorios
- **Estadísticas**: Métricas de uso y disponibilidad
- **Configuración**: Reglas de reserva y disponibilidad

### ✅ Servicios de Notificación
- **Email**: Templates HTML con nodemailer
- **Slack**: Integración con botones interactivos
- **WhatsApp**: Placeholder para hilos.com
- **Orquestador**: Sistema unificado de notificaciones

## 🔧 Configuración y Desarrollo

### Variables de Entorno Requeridas
```env
# Database
DATABASE_URL="mongodb://localhost:27017/ocn-house"

# Email Service
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Slack Integration
SLACK_BOT_TOKEN="xoxb-your-bot-token"
SLACK_SIGNING_SECRET="your-signing-secret"
SLACK_CHANNEL_ID="your-channel-id"

# WhatsApp (hilos.com)
WHATSAPP_API_KEY="your-api-key"
WHATSAPP_PHONE_NUMBER="your-phone-number"

# Better Auth
AUTH_SECRET="your-auth-secret"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### Comandos de Desarrollo
```bash
# Instalar dependencias
bun install

# Configurar base de datos
bunx prisma generate
bunx prisma db push

# Desarrollo
bun dev

# Build
bun build

# Tests
bun test
```

## 📊 Esquema de Base de Datos

### Modelos Principales
- **User**: Usuarios del sistema con Google SSO
- **MeetingRoom**: Salas de reunión con ubicación y reglas
- **Desk**: Escritorios con tipos y equipamiento
- **Booking**: Reservas de recursos con estados
- **Guest**: Huéspedes externos con check-in/out
- **Notification**: Historial de notificaciones enviadas

### Relaciones Clave
- User → Bookings (1:N)
- User → Guests (1:N) 
- Booking → Guest (1:N) - opcional
- MeetingRoom/Desk → Bookings (1:N)

## 🎨 Diseño y UX

### Patrones de Diseño
- **Dashboard-nested routes**: Todas las páginas bajo `/dashboard/`
- **Responsive design**: Mobile-first con Tailwind CSS
- **Loading states**: Skeletons y spinners consistentes
- **Error handling**: Toast notifications con Sonner
- **Empty states**: Ilustraciones y CTAs claros

### Componentes Reutilizables
- **Cards**: Información estructurada
- **Tables**: Listas con paginación y filtros
- **Forms**: Validación con React Hook Form + Zod
- **Modals**: Confirmaciones y formularios
- **Badges**: Estados y categorías
- **Calendars**: React Big Calendar integrado

## 🔄 Flujos de Trabajo

### Reserva de Sala de Reunión
1. Usuario selecciona tipo de recurso
2. Elige fecha/hora en calendario
3. Selecciona sala disponible
4. Completa detalles de reserva
5. Opcionalmente invita huéspedes
6. Sistema valida disponibilidad
7. Crea reserva y envía notificaciones

### Invitación de Huéspedes
1. Usuario completa formulario de invitación
2. Sistema genera invitación con QR/código
3. Envía email y notificación Slack
4. Huésped confirma asistencia
5. Recepción gestiona check-in/out
6. Sistema actualiza estados automáticamente

### Gestión de Recepción
1. Dashboard muestra huéspedes del día
2. Recepcionista busca/filtra huéspedes
3. Realiza check-in al llegar
4. Gestiona check-out al salir
5. Sistema actualiza métricas en tiempo real

## 📱 Páginas Implementadas

### Públicas
- `/` - Login con Google OAuth (blur background)

### Dashboard
- `/dashboard` - Overview con métricas y próximas reservas
- `/dashboard/bookings` - Lista de reservas del usuario
- `/dashboard/bookings/new` - Crear nueva reserva con calendario
- `/dashboard/meeting-rooms` - Explorar salas disponibles
- `/dashboard/meeting-rooms/[id]` - Detalle de sala con calendario
- `/dashboard/desks` - Explorar escritorios disponibles
- `/dashboard/guests` - Gestión de huéspedes invitados
- `/dashboard/guests/new` - Invitar nuevo huésped
- `/dashboard/reception` - Dashboard de recepción

### Administración
- `/dashboard/admin` - Panel principal de administración
- `/dashboard/admin/meeting-rooms` - Gestión de salas
- `/dashboard/admin/meeting-rooms/new` - Crear nueva sala
- `/dashboard/admin/desks` - Gestión de escritorios
- `/dashboard/admin/desks/new` - Crear nuevo escritorio

## 🔌 API Endpoints

Todos los endpoints están documentados en el archivo `api-endpoints.http` con ejemplos completos de uso.

### Estructura de Respuesta
```typescript
// Éxito
{
  success: true,
  data: T,
  pagination?: PaginationInfo
}

// Error
{
  success: false,
  error: string
}
```

## 🚀 Próximos Pasos

### Pendientes de Implementación
1. **Integración Better Auth**: Conectar Google OAuth con login
2. **Formularios de edición**: Para salas y escritorios
3. **Páginas de detalle**: Para escritorios y reservas individuales
4. **Validación de conflictos**: En tiempo real en calendario
5. **Notificaciones push**: Para confirmaciones y recordatorios
6. **Reportes**: Analytics de uso de espacios
7. **Configuración**: Panel de ajustes del sistema

### Mejoras Sugeridas
1. **Tests**: Unit y integration tests
2. **Performance**: Optimización de queries y caching
3. **Accesibilidad**: ARIA labels y navegación por teclado
4. **PWA**: Service workers para uso offline
5. **Internacionalización**: Soporte multi-idioma
6. **Audit logs**: Historial de cambios importantes

## 📞 Soporte

Para dudas o problemas:
1. Revisar esta documentación
2. Consultar el archivo `api-endpoints.http`
3. Verificar logs en consola del navegador
4. Revisar logs del servidor en terminal

---

**Desarrollado para OneCarNow** - Sistema de gestión de oficinas interno
