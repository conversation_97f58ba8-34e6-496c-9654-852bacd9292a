{"name": "ocn-house", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 4002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@elysiajs/eden": "^1.3.2", "@elysiajs/swagger": "^1.3.1", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@slack/web-api": "^7.9.3", "@tanstack/react-query": "^5.81.5", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "elysia": "^1.3.5", "lucide-react": "^0.525.0", "luxon": "^3.6.1", "next": "15.3.4", "nodemailer": "^7.0.4", "prisma": "^6.11.0", "react": "^19.0.0", "react-big-calendar": "^1.19.4", "react-dom": "^19.0.0", "react-email": "^4.0.17", "react-hook-form": "^7.59.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/luxon": "^3.6.2", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}