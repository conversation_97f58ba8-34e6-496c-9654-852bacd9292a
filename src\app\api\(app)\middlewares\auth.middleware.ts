
// import { auth } from '@/lib/auth';
// import { Elysia } from 'elysia';

import { auth } from '@/lib/auth';
import Elysia from 'elysia';

// export const authMiddleware = new Elysia()
//   .derive(async ({ request, status }) => {
//     console.log('headers: ', request.headers);
//     const session = await auth.api.getSession({
//       headers: request.headers
//     });
//     if (!session) {
//       return status(401);
//     }

//     return {
//       user: session.user,
//       session: session.session
//     };
//   });

/** 
 * Auth middleware and extend context with user and session, 
 * this makes the authentication middleware and make organizationId available in all controllers
 * @param app Elysia instance
 */

export const authMiddleware = (app: Elysia) =>
  app.derive(async (ctx) => {


    const session = await auth.api.getSession({
      headers: ctx.request.headers
    });

    if (!session) {
      throw new Error('Not authorized')
    }

    return session;
  })
